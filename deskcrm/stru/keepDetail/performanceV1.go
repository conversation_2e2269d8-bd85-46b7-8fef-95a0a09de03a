package keepDetail

import (
	"deskcrm/api/dal"
	"deskcrm/service/innerapi/course"
)

// BasicData 基础数据结构
type BasicData struct {
	CourseId           int64                           // 课程ID
	LessonIds          []int64                         // 章节ID列表
	StudentId          int64                           // 学生ID
	CourseLessonInfos  dal.CourseInfo                  // 课程章节信息
	CourseRecordConfig course.CourseRecordConfigResult // 课程记录配置
}
